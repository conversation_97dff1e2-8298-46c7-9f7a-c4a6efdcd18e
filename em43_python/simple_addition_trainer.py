"""
Simplified addition trainer with better debugging and smaller problem scope.
"""

import numpy as np
import time
from tqdm import tqdm
from em43_numba import _simulate_addition, _sanitize_rule, _sanitize_programme

# Start with the absolute simplest case
SIMPLE_INPUTS_A = np.array([1, 1, 2], dtype=np.int64)
SIMPLE_INPUTS_B = np.array([1, 2, 1], dtype=np.int64) 
SIMPLE_TARGETS = SIMPLE_INPUTS_A + SIMPLE_INPUTS_B

print(f"Training on simple cases: {list(zip(SIMPLE_INPUTS_A, SIMPLE_INPUTS_B, SIMPLE_TARGETS))}")

# Smaller parameters for debugging
WINDOW = 200
MAX_STEPS = 400
HALT_THRESH = 0.50
POP_SIZE = 50
L_PROG = 8  # Much shorter program
GENERATIONS = 200

def evaluate_genome(rule, prog):
    """Evaluate a single genome and return detailed info."""
    outputs = _simulate_addition(rule, prog, SIMPLE_INPUTS_A, SIMPLE_INPUTS_B, 
                                WINDOW, MAX_STEPS, HALT_THRESH)
    
    correct = 0
    convergent = 0
    total_error = 0
    
    for i, (pred, target) in enumerate(zip(outputs, SIMPLE_TARGETS)):
        if pred != -10:
            convergent += 1
            if pred == target:
                correct += 1
            else:
                total_error += abs(pred - target)
    
    # Simple fitness: prioritize convergence first, then correctness
    if convergent == 0:
        fitness = -100  # Heavily penalize non-convergence
    else:
        accuracy = correct / len(outputs)
        convergence_rate = convergent / len(outputs)
        avg_error = total_error / len(outputs) if convergent > 0 else 10
        
        fitness = (accuracy * 50) + (convergence_rate * 30) - (avg_error * 5)
    
    return fitness, outputs, correct, convergent

def random_genome():
    """Generate a random genome."""
    rng = np.random.default_rng()
    rule = rng.integers(0, 4, 64, dtype=np.uint8)
    prog = rng.choice([0, 1, 2], size=L_PROG, p=[0.8, 0.15, 0.05])  # Favor zeros
    return _sanitize_rule(rule), _sanitize_programme(prog)

def mutate_genome(rule, prog, rate=0.1):
    """Mutate a genome with lower rate."""
    new_rule = rule.copy()
    new_prog = prog.copy()
    
    rng = np.random.default_rng()
    
    # Mutate rule
    mask = rng.random(64) < rate
    new_rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
    
    # Mutate program  
    mask = rng.random(L_PROG) < rate
    new_prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.8, 0.15, 0.05])
    
    return _sanitize_rule(new_rule), _sanitize_programme(new_prog)

def simple_training():
    """Run simplified training."""
    print(f"Starting simple training: {POP_SIZE} genomes, {GENERATIONS} generations")
    print(f"Program length: {L_PROG}, Window: {WINDOW}, Max steps: {MAX_STEPS}")
    
    # Initialize population
    population = []
    fitness_scores = []
    
    print("Initializing population...")
    for i in range(POP_SIZE):
        rule, prog = random_genome()
        fitness, outputs, correct, convergent = evaluate_genome(rule, prog)
        population.append((rule, prog))
        fitness_scores.append(fitness)
        
        if i < 5:  # Show first few
            print(f"  Genome {i}: fitness={fitness:.2f}, outputs={outputs}, correct={correct}, convergent={convergent}")
    
    best_fitness = max(fitness_scores)
    best_idx = np.argmax(fitness_scores)
    best_genome = population[best_idx]
    
    print(f"Initial best fitness: {best_fitness:.2f}")
    
    # Evolution loop
    for gen in tqdm(range(GENERATIONS), desc="Training"):
        # Selection and reproduction
        fitness_array = np.array(fitness_scores)
        
        # Select top 20%
        n_elite = max(1, POP_SIZE // 5)
        elite_indices = np.argsort(fitness_array)[-n_elite:]
        
        new_population = []
        new_fitness = []
        
        # Keep elites
        for idx in elite_indices:
            new_population.append(population[idx])
            new_fitness.append(fitness_scores[idx])
        
        # Generate offspring
        rng = np.random.default_rng()
        while len(new_population) < POP_SIZE:
            # Select parent from top half
            parent_idx = rng.choice(elite_indices)
            parent_rule, parent_prog = population[parent_idx]
            
            # Mutate
            child_rule, child_prog = mutate_genome(parent_rule, parent_prog)
            
            # Evaluate
            fitness, outputs, correct, convergent = evaluate_genome(child_rule, child_prog)
            
            new_population.append((child_rule, child_prog))
            new_fitness.append(fitness)
        
        population = new_population
        fitness_scores = new_fitness
        
        # Track best
        gen_best_fitness = max(fitness_scores)
        if gen_best_fitness > best_fitness:
            best_fitness = gen_best_fitness
            best_idx = np.argmax(fitness_scores)
            best_genome = population[best_idx]
        
        # Progress report
        if gen % 50 == 0:
            avg_fitness = np.mean(fitness_scores)
            print(f"Gen {gen}: best={best_fitness:.2f}, avg={avg_fitness:.2f}")
            
            # Test best genome
            best_rule, best_prog = best_genome
            _, best_outputs, best_correct, best_convergent = evaluate_genome(best_rule, best_prog)
            print(f"  Best outputs: {best_outputs} (correct: {best_correct}, convergent: {best_convergent})")
    
    # Final evaluation
    print(f"\nTraining completed!")
    print(f"Best fitness: {best_fitness:.2f}")
    
    best_rule, best_prog = best_genome
    final_fitness, final_outputs, final_correct, final_convergent = evaluate_genome(best_rule, best_prog)
    
    print(f"Final best genome:")
    print(f"  Inputs:  {SIMPLE_INPUTS_A} + {SIMPLE_INPUTS_B}")
    print(f"  Expected: {SIMPLE_TARGETS}")
    print(f"  Actual:   {final_outputs}")
    print(f"  Correct:  {final_correct}/{len(SIMPLE_TARGETS)}")
    print(f"  Convergent: {final_convergent}/{len(SIMPLE_TARGETS)}")
    
    # Test on slightly larger numbers
    test_a = np.array([3, 4], dtype=np.int64)
    test_b = np.array([2, 3], dtype=np.int64)
    test_targets = test_a + test_b
    
    test_outputs = _simulate_addition(best_rule, best_prog, test_a, test_b,
                                     WINDOW, MAX_STEPS, HALT_THRESH)
    
    print(f"\nGeneralization test:")
    print(f"  Inputs:  {test_a} + {test_b}")
    print(f"  Expected: {test_targets}")
    print(f"  Actual:   {test_outputs}")
    
    test_correct = np.sum((test_outputs != -10) & (test_outputs == test_targets))
    test_convergent = np.sum(test_outputs != -10)
    print(f"  Correct:  {test_correct}/{len(test_targets)}")
    print(f"  Convergent: {test_convergent}/{len(test_targets)}")
    
    return best_genome, best_fitness

if __name__ == "__main__":
    start_time = time.time()
    best_genome, best_fitness = simple_training()
    end_time = time.time()
    
    print(f"\nTotal training time: {end_time - start_time:.1f} seconds")
    
    if best_fitness > 0:
        print("SUCCESS: Found a working addition model!")
    else:
        print("Training did not find a good solution. Consider:")
        print("- Increasing population size or generations")
        print("- Adjusting mutation rate")
        print("- Simplifying the problem further")
        print("- Checking the simulation implementation")
