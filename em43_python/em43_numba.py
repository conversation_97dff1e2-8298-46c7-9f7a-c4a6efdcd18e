"""
em43_numba.py  -  Numba-accelerated batched EM-4/3 simulator
==================================================================
Drop-in replacement for the original **em43_parallel.py**.  
Public API unchanged:

    from em43_numba import EM43Batch, _sanitize_rule, _sanitize_programme

Key details
-----------
* 1-D CA, 4 states, radius-1, open boundary, 2-cell separator “BB”.
* Evaluates **B inputs in parallel** for a single genome.
* Core simulation loop is compiled with <PERSON>umba (`@njit(cache=True)`).
* First call takes a few 100 ms to compile, then runs 5-10x faster.

No bit-packing; all arrays are `uint8`.  First & last columns stay blank.

Author: <PERSON> - with the help of ChatGPT

this code has not been checked - may still present unexpected behaviours
"""

from __future__ import annotations
from typing import List, <PERSON>ple
import numpy as np
import numba as nb

nb.set_num_threads(nb.config.NUMBA_NUM_THREADS)   # use all available
print(f"Numba using {nb.get_num_threads()} threads")


# ────────────────── helpers & constants ──────────────────────────────
def lut_idx(l: int, c: int, r: int) -> int:              # 3-tuple → 0..63
    return (l << 4) | (c << 2) | r


SEPARATOR = np.array([3, 3], dtype=np.uint8)             # BB

_IMMUTABLE = {                                           # hard-wired LUT rows
    lut_idx(0, 0, 0): 0,
    lut_idx(0, 2, 0): 2,
    lut_idx(0, 0, 2): 0,
    lut_idx(2, 0, 0): 0,
    lut_idx(0, 3, 3): 3,
    lut_idx(3, 3, 0): 3,
    lut_idx(0, 0, 3): 0,
    lut_idx(3, 0, 0): 0,
}

def _sanitize_rule(rule: np.ndarray) -> np.ndarray:
    """Overwrite immutable LUT entries; clip to 0-3."""
    rule = rule.astype(np.uint8, copy=True)
    for k, v in _IMMUTABLE.items():
        rule[k] = v
    rule[rule > 3] &= 3
    return rule

def _sanitize_programme(prog: np.ndarray) -> np.ndarray:
    """Remove accidental blue cells from programme."""
    prog = prog.astype(np.uint8, copy=True)
    prog[prog == 3] = 0
    return prog


# ────────────────── Numba simulation kernel ──────────────────────────
@nb.njit(cache=True)
def _simulate(rule: np.ndarray,
              prog: np.ndarray,
              inputs: np.ndarray,
              window: int,
              max_steps: int,
              halt_th: float) -> np.ndarray:
    """
    Parameters
    ----------
    rule    : (64,) uint8
    prog    : (L,)  uint8
    inputs  : (B,) int64     (values 1..30)
    Returns
    -------
    outputs : (B,) int32     (-10 on failure)
    """
    L = prog.shape[0]
    B = inputs.shape[0]
    N = window

    state   = np.zeros((B, N), np.uint8)
    halted  = np.zeros(B, np.bool_)
    frozen  = np.zeros_like(state)
    output  = np.full(B, -10, np.int32)

    # write programme & separator
    for b in range(B):
        for j in range(L):
            state[b, j] = prog[j]
        state[b, L    ] = 3     # B
        state[b, L + 1] = 3     # B

    # write beacons 0^(n+1) R 0
    for b in range(B):
        r_idx = L + 2 + inputs[b] + 1
        state[b, r_idx] = 2

    # main loop
    for _ in range(max_steps):
        active_any = False
        for b in range(B):
            if halted[b]:
                continue
            active_any = True
            nxt = np.zeros(N, np.uint8)
            for x in range(1, N - 1):
                idx = (state[b, x-1] << 4) | (state[b, x] << 2) | state[b, x+1]
                nxt[x] = rule[idx]
            state[b] = nxt

            # halting check
            live = blue = 0
            for x in range(N):
                v = nxt[x]
                if v != 0:
                    live += 1
                    if v == 3:
                        blue += 1
            if live > 0 and blue / live >= halt_th:
                halted[b] = True
                frozen[b] = nxt

        if not active_any:
            break

    # decode outputs
    for b in range(B):
        if not halted[b]:
            continue
        rpos = -1
        for x in range(N - 1, -1, -1):
            if frozen[b, x] == 2:
                rpos = x
                break
        if rpos != -1:
            output[b] = rpos - (L + 3)          # (sep=2)+1 zeros before R

    return output


# ────────────────── OO wrapper (same API) ────────────────────────────
class EM43Batch:
    """
    Evaluate a single genome on B inputs in parallel (Numba backend).

    Parameters
    ----------
    genome      : (rule_array, programme_array)
    window      : int   tape length
    max_steps   : int
    halt_thresh : float
    """

    def __init__(self,
                 genome: Tuple[np.ndarray, np.ndarray],
                 window: int = 500,
                 max_steps: int = 256,
                 halt_thresh: float = 0.50):
        rule, prog = genome
        self.rule  = _sanitize_rule(rule)
        self.prog  = _sanitize_programme(prog)
        self.L     = len(self.prog)

        if self.L + 5 >= window:             # L + BB + 0 R 0  needs ≥5 extra
            raise ValueError("window too small for given programme length")

        self.N           = window
        self.max_steps   = max_steps
        self.halt_thresh = halt_thresh

    # -----------------------------------------------------------------
    def run(self, inputs: List[int]) -> np.ndarray:
        """Compute doubling outputs for the given input list."""
        return _simulate(self.rule,
                         self.prog,
                         np.asarray(inputs, dtype=np.int64),
                         self.N,
                         self.max_steps,
                         self.halt_thresh)


# ────────────────── Two-input simulation for GCD/LCM ────────────────
@nb.njit(cache=True)
def _simulate_two_inputs(rule: np.ndarray,
                        prog: np.ndarray,
                        inputs_a: np.ndarray,
                        inputs_b: np.ndarray,
                        window: int,
                        max_steps: int,
                        halt_th: float) -> np.ndarray:
    """
    Two-input simulation for operations like GCD/LCM.

    Tape structure: [program] BB 0^(a+1) R 0^(b+1) R 0

    Parameters
    ----------
    rule      : (64,) uint8
    prog      : (L,)  uint8
    inputs_a  : (B,) int64     (first input values)
    inputs_b  : (B,) int64     (second input values)
    Returns
    -------
    outputs   : (B,) int32     (-10 on failure)
    """
    L = prog.shape[0]
    B = inputs_a.shape[0]
    N = window

    state   = np.zeros((B, N), np.uint8)
    halted  = np.zeros(B, np.bool_)
    frozen  = np.zeros_like(state)
    output  = np.full(B, -10, np.int32)

    # write programme & separator
    for b in range(B):
        for j in range(L):
            state[b, j] = prog[j]
        state[b, L    ] = 3     # B
        state[b, L + 1] = 3     # B

    # write two beacons: 0^(a+1) R 0^(b+1) R 0
    for b in range(B):
        # First beacon: a+1 zeros, then R
        r1_idx = L + 2 + inputs_a[b] + 1
        state[b, r1_idx] = 2

        # Second beacon: b+1 zeros after first R, then second R
        r2_idx = r1_idx + 1 + inputs_b[b] + 1
        if r2_idx < N:
            state[b, r2_idx] = 2

    # main loop
    for _ in range(max_steps):
        active_any = False
        for b in range(B):
            if halted[b]:
                continue
            active_any = True
            nxt = np.zeros(N, np.uint8)
            for x in range(1, N - 1):
                idx = (state[b, x-1] << 4) | (state[b, x] << 2) | state[b, x+1]
                nxt[x] = rule[idx]
            state[b] = nxt

            # halting check
            live = blue = 0
            for x in range(N):
                v = nxt[x]
                if v != 0:
                    live += 1
                    if v == 3:
                        blue += 1
            if live > 0 and blue / live >= halt_th:
                halted[b] = True
                frozen[b] = nxt

        if not active_any:
            break

    # decode outputs - find rightmost R and decode relative to last BB
    for b in range(B):
        if not halted[b]:
            continue
        rpos = -1
        for x in range(N - 1, -1, -1):
            if frozen[b, x] == 2:
                rpos = x
                break
        if rpos != -1:
            # Find the position after the second input beacon
            a_val = inputs_a[b]
            b_val = inputs_b[b]
            expected_second_r = L + 2 + a_val + 1 + 1 + b_val + 1
            output[b] = rpos - expected_second_r

    return output


class EM43TwoInputBatch:
    """
    Evaluate a single genome on pairs of inputs for two-input operations like GCD/LCM.

    Tape structure: [program] BB 0^(a+1) R 0^(b+1) R 0
    """

    def __init__(self,
                 genome: Tuple[np.ndarray, np.ndarray],
                 window: int = 800,
                 max_steps: int = 512,
                 halt_thresh: float = 0.50):
        rule, prog = genome
        self.rule  = _sanitize_rule(rule)
        self.prog  = _sanitize_programme(prog)
        self.L     = len(self.prog)

        # Need more space for two inputs: L + BB + max_a + R + max_b + R + output_space
        if self.L + 10 >= window:
            raise ValueError("window too small for given programme length and two inputs")

        self.N           = window
        self.max_steps   = max_steps
        self.halt_thresh = halt_thresh

    def run(self, inputs_a: List[int], inputs_b: List[int]) -> np.ndarray:
        """Compute outputs for pairs of inputs (a, b)."""
        if len(inputs_a) != len(inputs_b):
            raise ValueError("inputs_a and inputs_b must have the same length")

        return _simulate_two_inputs(self.rule,
                                   self.prog,
                                   np.asarray(inputs_a, dtype=np.int64),
                                   np.asarray(inputs_b, dtype=np.int64),
                                   self.N,
                                   self.max_steps,
                                   self.halt_thresh)


# ────────────────── Addition-specific simulation ────────────────────
@nb.njit(cache=True)
def _simulate_addition(rule: np.ndarray,
                      prog: np.ndarray,
                      inputs_a: np.ndarray,
                      inputs_b: np.ndarray,
                      window: int,
                      max_steps: int,
                      halt_th: float) -> np.ndarray:
    """
    Addition simulation: a + b = result

    Tape structure: [program] BB 0^(a+1) R 0^(b+1) R 0
    Output decoding: Count zeros between final R and end of computation

    Parameters
    ----------
    rule      : (64,) uint8
    prog      : (L,)  uint8
    inputs_a  : (B,) int64     (first addend)
    inputs_b  : (B,) int64     (second addend)
    Returns
    -------
    outputs   : (B,) int32     (-10 on failure)
    """
    L = prog.shape[0]
    B = inputs_a.shape[0]
    N = window

    state   = np.zeros((B, N), np.uint8)
    halted  = np.zeros(B, np.bool_)
    frozen  = np.zeros_like(state)
    output  = np.full(B, -10, np.int32)

    # write programme & separator
    for b in range(B):
        for j in range(L):
            state[b, j] = prog[j]
        state[b, L    ] = 3     # B
        state[b, L + 1] = 3     # B

    # write two input encodings: 0^(a+1) R 0^(b+1) R 0
    for b in range(B):
        # First input: a+1 zeros, then R
        r1_idx = L + 2 + inputs_a[b] + 1
        if r1_idx < N:
            state[b, r1_idx] = 2

        # Second input: b+1 zeros after first R, then second R
        r2_idx = r1_idx + 1 + inputs_b[b] + 1
        if r2_idx < N:
            state[b, r2_idx] = 2

    # main loop
    for _ in range(max_steps):
        active_any = False
        for b in range(B):
            if halted[b]:
                continue
            active_any = True
            nxt = np.zeros(N, np.uint8)
            for x in range(1, N - 1):
                idx = (state[b, x-1] << 4) | (state[b, x] << 2) | state[b, x+1]
                nxt[x] = rule[idx]
            state[b] = nxt

            # halting check
            live = blue = 0
            for x in range(N):
                v = nxt[x]
                if v != 0:
                    live += 1
                    if v == 3:
                        blue += 1
            if live > 0 and blue / live >= halt_th:
                halted[b] = True
                frozen[b] = nxt

        if not active_any:
            break

    # decode outputs for addition - count zeros after final R
    for b in range(B):
        if not halted[b]:
            continue

        # Find rightmost R (final result marker)
        final_r_pos = -1
        for x in range(N - 1, -1, -1):
            if frozen[b, x] == 2:
                final_r_pos = x
                break

        if final_r_pos != -1:
            # Count consecutive zeros after the final R
            zero_count = 0
            for x in range(final_r_pos + 1, N):
                if frozen[b, x] == 0:
                    zero_count += 1
                else:
                    break

            # The result is the number of zeros (representing a+b)
            output[b] = zero_count

    return output


class EM43AdditionBatch:
    """
    Evaluate a single genome on pairs of inputs for addition: a + b = result

    Tape structure: [program] BB 0^(a+1) R 0^(b+1) R 0
    Output: Count zeros after final R marker
    """

    def __init__(self,
                 genome: Tuple[np.ndarray, np.ndarray],
                 window: int = 800,
                 max_steps: int = 512,
                 halt_thresh: float = 0.50):
        rule, prog = genome
        self.rule  = _sanitize_rule(rule)
        self.prog  = _sanitize_programme(prog)
        self.L     = len(self.prog)

        # Need space for: L + BB + max_a + R + max_b + R + result_space
        if self.L + 20 >= window:  # More generous for addition results
            raise ValueError("window too small for given programme length and addition")

        self.N           = window
        self.max_steps   = max_steps
        self.halt_thresh = halt_thresh

    def run(self, inputs_a: List[int], inputs_b: List[int]) -> np.ndarray:
        """Compute addition outputs for pairs of inputs (a, b)."""
        if len(inputs_a) != len(inputs_b):
            raise ValueError("inputs_a and inputs_b must have the same length")

        return _simulate_addition(self.rule,
                                 self.prog,
                                 np.asarray(inputs_a, dtype=np.int64),
                                 np.asarray(inputs_b, dtype=np.int64),
                                 self.N,
                                 self.max_steps,
                                 self.halt_thresh)


# ────────────────── quick demo ───────────────────────────────────────
if __name__ == "__main__":
    rng = np.random.default_rng()
    rule = rng.integers(0, 4, 64, dtype=np.uint8)
    prog = rng.choice([0, 1, 2], size=32, p=[0.7, 0.2, 0.1])
    sim  = EM43Batch((rule, prog), window=300, max_steps=256)
    print("outputs 1..30:", sim.run(list(range(1, 31))))

    # Test two-input version (GCD/LCM)
    sim2 = EM43TwoInputBatch((rule, prog), window=800, max_steps=512)
    print("two-input test:", sim2.run([3, 6], [4, 8]))

    # Test addition version
    sim_add = EM43AdditionBatch((rule, prog), window=800, max_steps=512)
    print("addition test:", sim_add.run([3, 6], [4, 8]))